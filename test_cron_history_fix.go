package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"net/http/httptest"
	"os"
	"time"

	"github.com/think-root/content-maestro/internal/models"
	"github.com/think-root/content-maestro/internal/server"
	"github.com/think-root/content-maestro/internal/store"
)

func main() {
	// Create temporary database
	tempDir := "/tmp/test_cron_history"
	os.RemoveAll(tempDir)
	defer os.RemoveAll(tempDir)

	// Initialize store
	testStore, err := store.NewStore(tempDir)
	if err != nil {
		log.Fatal("Failed to create store:", err)
	}
	defer testStore.Close()

	// Create API instance
	api := server.NewCronAPI(testStore, nil)

	// Add some test data
	testStore.LogCronExecution("collect", true, "")
	time.Sleep(10 * time.Millisecond)
	testStore.LogCronExecution("collect", false, "test error")
	time.Sleep(10 * time.Millisecond)
	testStore.LogCronExecution("collect", true, "")
	time.Sleep(10 * time.Millisecond)
	testStore.LogCronExecution("message", false, "another error")

	// Test 1: Get all history (no success filter)
	fmt.Println("=== Test 1: All history ===")
	req1 := httptest.NewRequest("GET", "/api/cron-history?page=1&pageSize=10", nil)
	w1 := httptest.NewRecorder()
	api.GetCronHistory(w1, req1)

	var result1 []models.CronHistory
	json.Unmarshal(w1.Body.Bytes(), &result1)
	fmt.Printf("Total records: %d\n", len(result1))
	for _, h := range result1 {
		fmt.Printf("- %s: success=%t\n", h.Name, h.Success)
	}

	// Test 2: Get only failed executions (success=false)
	fmt.Println("\n=== Test 2: Only failed executions ===")
	req2 := httptest.NewRequest("GET", "/api/cron-history?page=1&pageSize=10&success=false", nil)
	w2 := httptest.NewRecorder()
	api.GetCronHistory(w2, req2)

	var result2 []models.CronHistory
	json.Unmarshal(w2.Body.Bytes(), &result2)
	fmt.Printf("Failed records: %d\n", len(result2))
	for _, h := range result2 {
		fmt.Printf("- %s: success=%t\n", h.Name, h.Success)
	}

	// Test 3: Get only successful executions (success=true)
	fmt.Println("\n=== Test 3: Only successful executions ===")
	req3 := httptest.NewRequest("GET", "/api/cron-history?page=1&pageSize=10&success=true", nil)
	w3 := httptest.NewRecorder()
	api.GetCronHistory(w3, req3)

	var result3 []models.CronHistory
	json.Unmarshal(w3.Body.Bytes(), &result3)
	fmt.Printf("Successful records: %d\n", len(result3))
	for _, h := range result3 {
		fmt.Printf("- %s: success=%t\n", h.Name, h.Success)
	}

	// Verify the fix
	fmt.Println("\n=== Verification ===")
	if len(result1) == len(result2)+len(result3) {
		fmt.Println("✅ SUCCESS: Filter works correctly!")
		fmt.Printf("Total: %d = Failed: %d + Successful: %d\n", len(result1), len(result2), len(result3))
	} else {
		fmt.Println("❌ FAILED: Filter is still broken!")
		fmt.Printf("Total: %d ≠ Failed: %d + Successful: %d\n", len(result1), len(result2), len(result3))
	}
}
